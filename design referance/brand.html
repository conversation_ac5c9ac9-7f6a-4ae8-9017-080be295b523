<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>CozyWish - Design System Showcase</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="description" content="CozyWish Design System - Professional spa and wellness marketplace design components">

  <!-- Bootstrap 5 -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">

  <!-- Font Awesome -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- CozyWish Design System Styles -->
  <style>
    /* ===== COZY WISH DESIGN SYSTEM ===== */

    /* CSS Custom Properties */
    :root {
      /* Brand Colors */
      --cw-brand-primary: #2F160F;
      --cw-brand-primary-light: #4a2a1f;
      --cw-brand-primary-dark: #1a0d08;
      --cw-brand-accent: #fae1d7;
      --cw-brand-accent-light: #fef7f0;
      --cw-brand-accent-dark: #f1d4c4;

      /* Primary Colors */
      --cw-primary-50: #fef7f0;
      --cw-primary-100: #fdeee0;
      --cw-primary-200: #fad9c0;
      --cw-primary-300: #f6be95;
      --cw-primary-400: #f19968;
      --cw-primary-500: #ed7544;
      --cw-primary-600: #de5a2c;
      --cw-primary-700: #b84622;
      --cw-primary-800: #933a22;
      --cw-primary-900: #76321f;
      --cw-primary-950: #40180e;

      /* Secondary Colors */
      --cw-secondary-50: #f9f7f4;
      --cw-secondary-100: #f1ebe2;
      --cw-secondary-200: #e3d5c4;
      --cw-secondary-300: #d1b89e;
      --cw-secondary-400: #bc9876;
      --cw-secondary-500: #ad7f5a;
      --cw-secondary-600: #a0704e;
      --cw-secondary-700: #855a42;
      --cw-secondary-800: #6c4a39;
      --cw-secondary-900: #583d30;
      --cw-secondary-950: #2f1f18;

      /* Neutral Colors */
      --cw-neutral-50: #fafafa;
      --cw-neutral-100: #f5f5f5;
      --cw-neutral-200: #e5e5e5;
      --cw-neutral-300: #d4d4d4;
      --cw-neutral-400: #a3a3a3;
      --cw-neutral-500: #737373;
      --cw-neutral-600: #525252;
      --cw-neutral-700: #404040;
      --cw-neutral-800: #262626;
      --cw-neutral-900: #171717;
      --cw-neutral-950: #0a0a0a;

      /* Semantic Colors */
      --cw-success: #059669;
      --cw-warning: #d97706;
      --cw-error: #dc2626;
      --cw-info: #0284c7;

      /* Typography */
      --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

      /* Shadows */
      --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --cw-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --cw-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

      /* Gradients */
      --cw-gradient-hero: radial-gradient(ellipse at center, #fae1d7 40%, #fef7f0 70%, #ffffff 100%);
      --cw-gradient-hero-alt: radial-gradient(ellipse at center, #fef7f0 0%, #fdeee0 40%, #ffffff 100%);
      --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, #fae1d7 100%);
      --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
      --cw-gradient-button: linear-gradient(135deg, #ed7544 0%, #de5a2c 100%);
      --cw-gradient-brand-button: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
      --cw-gradient-accent: linear-gradient(135deg, #fae1d7 0%, #f1d4c4 100%);
    }

    /* Global Styles */
    body {
      font-family: var(--cw-font-primary);
      line-height: 1.6;
      color: var(--cw-neutral-800);
      background: var(--cw-gradient-hero);
      min-height: 100vh;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
      font-family: var(--cw-font-heading);
      font-weight: 600;
      color: var(--cw-secondary-950);
      line-height: 1.3;
    }

    .display-font {
      font-family: var(--cw-font-display);
    }

    /* Brand Colors */
    .text-brand-cw { color: var(--cw-brand-primary) !important; }
    .text-brand-light-cw { color: var(--cw-brand-primary-light) !important; }
    .text-primary-cw { color: var(--cw-primary-500) !important; }
    .text-secondary-cw { color: var(--cw-secondary-950) !important; }
    .text-neutral-cw { color: var(--cw-neutral-600) !important; }
    .bg-brand-cw { background-color: var(--cw-brand-primary) !important; }
    .bg-brand-accent-cw { background-color: var(--cw-brand-accent) !important; }
    .bg-primary-cw { background-color: var(--cw-primary-500) !important; }
    .bg-light-cw { background-color: var(--cw-primary-50) !important; }

    /* Custom Buttons */
    .btn-cw-primary {
      background: var(--cw-gradient-brand-button);
      border: none;
      border-radius: 0.5rem;
      font-weight: 600;
      padding: 0.75rem 1.5rem;
      color: white;
      transition: all 0.2s ease;
      box-shadow: var(--cw-shadow-sm);
    }
    .btn-cw-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
      color: white;
    }

    .btn-cw-brand {
      background: var(--cw-gradient-brand-button);
      border: none;
      border-radius: 0.5rem;
      font-weight: 600;
      padding: 0.75rem 1.5rem;
      color: white;
      transition: all 0.2s ease;
      box-shadow: var(--cw-shadow-sm);
    }
    .btn-cw-brand:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
      color: white;
    }

    .btn-cw-secondary {
      border: 2px solid var(--cw-brand-primary);
      color: var(--cw-brand-primary);
      border-radius: 0.5rem;
      font-weight: 600;
      padding: 0.75rem 1.5rem;
      background: white;
      transition: all 0.2s ease;
    }
    .btn-cw-secondary:hover {
      background: var(--cw-brand-primary);
      color: white;
      transform: translateY(-1px);
    }

    .btn-cw-brand-outline {
      border: 2px solid var(--cw-brand-primary);
      color: var(--cw-brand-primary);
      border-radius: 0.5rem;
      font-weight: 600;
      padding: 0.75rem 1.5rem;
      background: white;
      transition: all 0.2s ease;
    }
    .btn-cw-brand-outline:hover {
      background: var(--cw-brand-primary);
      color: white;
      transform: translateY(-1px);
    }

    .btn-cw-orange {
      background: var(--cw-gradient-button);
      border: none;
      border-radius: 0.5rem;
      font-weight: 600;
      padding: 0.75rem 1.5rem;
      color: white;
      transition: all 0.2s ease;
      box-shadow: var(--cw-shadow-sm);
    }
    .btn-cw-orange:hover {
      transform: translateY(-1px);
      box-shadow: var(--cw-shadow-md);
      color: white;
    }

    .btn-cw-orange-outline {
      border: 2px solid var(--cw-primary-500);
      color: var(--cw-primary-500);
      border-radius: 0.5rem;
      font-weight: 600;
      padding: 0.75rem 1.5rem;
      background: white;
      transition: all 0.2s ease;
    }
    .btn-cw-orange-outline:hover {
      background: var(--cw-primary-500);
      color: white;
      transform: translateY(-1px);
    }

    .btn-cw-ghost {
      color: var(--cw-primary-500);
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border: none;
      background: transparent;
      transition: all 0.2s ease;
    }
    .btn-cw-ghost:hover {
      color: var(--cw-primary-600);
      text-decoration: underline;
    }

    /* Custom Cards */
    .card-cw {
      border: 1px solid var(--cw-neutral-200);
      border-radius: 1rem;
      box-shadow: var(--cw-shadow-md);
      background: white;
      overflow: hidden;
      transition: all 0.3s ease;
    }
    .card-cw:hover {
      transform: translateY(-2px);
      box-shadow: var(--cw-shadow-lg);
    }

    .card-cw-featured {
      border: 2px solid var(--cw-primary-500);
      background: var(--cw-gradient-card-subtle);
    }

    .card-cw-brand {
      border: 2px solid var(--cw-brand-primary);
      background: var(--cw-gradient-card);
      box-shadow: 0 10px 15px -3px rgba(47, 22, 15, 0.1);
    }

    .card-cw-accent {
      border: 1px solid var(--cw-brand-accent);
      background: var(--cw-brand-accent);
      box-shadow: var(--cw-shadow-sm);
    }

    /* Custom Forms */
    .form-control-cw {
      border: 2px solid var(--cw-neutral-200);
      border-radius: 0.5rem;
      padding: 0.75rem 1rem;
      font-size: 1rem;
      transition: all 0.2s ease;
    }
    .form-control-cw:focus {
      border-color: var(--cw-primary-500);
      box-shadow: 0 0 0 0.2rem rgba(237, 117, 68, 0.1);
    }

    /* Navigation */
    .navbar-cw {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      box-shadow: var(--cw-shadow-sm);
    }

    .navbar-brand-cw {
      font-family: var(--cw-font-heading);
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--cw-brand-primary);
    }

    .nav-link-cw {
      font-weight: 500;
      color: var(--cw-neutral-700);
      padding: 0.5rem 1rem;
      transition: color 0.2s ease;
    }
    .nav-link-cw:hover {
      color: var(--cw-primary-500);
    }

    /* Badges */
    .badge-cw-primary {
      background: var(--cw-primary-500);
      color: white;
      font-weight: 500;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
    }

    .badge-cw-secondary {
      background: var(--cw-neutral-100);
      color: var(--cw-neutral-700);
      font-weight: 500;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
    }

    /* Alerts */
    .alert-cw-success {
      background: #f0fdf4;
      border: 1px solid #bbf7d0;
      color: #166534;
      border-radius: 0.5rem;
    }

    .alert-cw-warning {
      background: #fffbeb;
      border: 1px solid #fed7aa;
      color: #92400e;
      border-radius: 0.5rem;
    }

    .alert-cw-error {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #991b1b;
      border-radius: 0.5rem;
    }

    /* Utility Classes */
    .shadow-cw-sm { box-shadow: var(--cw-shadow-sm); }
    .shadow-cw-md { box-shadow: var(--cw-shadow-md); }
    .shadow-cw-lg { box-shadow: var(--cw-shadow-lg); }

    .rounded-cw { border-radius: 0.5rem; }
    .rounded-cw-lg { border-radius: 1rem; }
    .rounded-cw-full { border-radius: 9999px; }

    /* Section Styling */
    .section-showcase {
      padding: 4rem 0;
      margin: 2rem 0;
    }

    .section-title {
      font-family: var(--cw-font-heading);
      font-weight: 700;
      color: var(--cw-secondary-950);
      margin-bottom: 3rem;
      position: relative;
    }

    .section-title::after {
      content: '';
      position: absolute;
      bottom: -0.5rem;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background: var(--cw-gradient-button);
      border-radius: 2px;
    }

    /* Color Palette Display */
    .color-swatch {
      width: 80px;
      height: 80px;
      border-radius: 0.5rem;
      box-shadow: var(--cw-shadow-md);
      margin-bottom: 0.5rem;
    }

    .color-info {
      font-size: 0.875rem;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }

    /* Component Preview Boxes */
    .preview-box {
      background: white;
      border: 1px solid var(--cw-neutral-200);
      border-radius: 1rem;
      padding: 2rem;
      margin: 1rem 0;
      box-shadow: var(--cw-shadow-sm);
    }

    .preview-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--cw-secondary-950);
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid var(--cw-primary-100);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .section-showcase {
        padding: 2rem 0;
      }

      .color-swatch {
        width: 60px;
        height: 60px;
      }
    }
  </style>
</head>

<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-cw sticky-top">
  <div class="container">
    <a class="navbar-brand navbar-brand-cw" href="#">CozyWish</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNav">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="mainNav">
      <ul class="navbar-nav ms-auto">
        <li class="nav-item"><a class="nav-link nav-link-cw" href="#brand">Brand</a></li>
        <li class="nav-item"><a class="nav-link nav-link-cw" href="#colors">Colors</a></li>
        <li class="nav-item"><a class="nav-link nav-link-cw" href="#typography">Typography</a></li>
        <li class="nav-item"><a class="nav-link nav-link-cw" href="#components">Components</a></li>
        <li class="nav-item"><a class="nav-link nav-link-cw" href="#layouts">Layouts</a></li>
      </ul>
      <button class="btn btn-cw-primary btn-sm ms-lg-3">Get Started</button>
    </div>
  </div>
</nav>

<!-- Hero Section -->
<header class="section-showcase text-center" id="brand">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <h1 class="display-3 fw-bold display-font text-secondary-cw mb-4">CozyWish Design System</h1>
        <p class="lead text-neutral-cw mb-5">Professional design system for spa and wellness marketplace. Built with Bootstrap 5, optimized for AI-assisted development.</p>
        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
          <button class="btn btn-cw-primary btn-lg">Explore Components</button>
          <button class="btn btn-cw-secondary btn-lg">View Documentation</button>
        </div>
      </div>
    </div>

    <!-- Brand Identity -->
    <div class="row mt-5 pt-5">
      <div class="col-lg-4 mb-4">
        <div class="card-cw h-100 text-center p-4">
          <i class="fas fa-spa text-primary-cw fa-3x mb-3"></i>
          <h5 class="fw-bold">Wellness Focused</h5>
          <p class="text-neutral-cw">Designed specifically for spa, massage, and wellness services</p>
        </div>
      </div>
      <div class="col-lg-4 mb-4">
        <div class="card-cw h-100 text-center p-4">
          <i class="fas fa-palette text-primary-cw fa-3x mb-3"></i>
          <h5 class="fw-bold">Professional Design</h5>
          <p class="text-neutral-cw">Warm, inviting colors with professional typography</p>
        </div>
      </div>
      <div class="col-lg-4 mb-4">
        <div class="card-cw h-100 text-center p-4">
          <i class="fas fa-code text-primary-cw fa-3x mb-3"></i>
          <h5 class="fw-bold">Bootstrap 5 Only</h5>
          <p class="text-neutral-cw">Pure Bootstrap 5 classes with minimal custom CSS</p>
        </div>
      </div>
    </div>
  </div>
</header>

<!-- Color Palette Section -->
<section class="section-showcase bg-white" id="colors">
  <div class="container">
    <h2 class="section-title text-center">Color Palette</h2>

    <!-- Brand Colors -->
    <div class="row mb-5">
      <div class="col-12">
        <h4 class="fw-bold mb-4 text-brand-cw">Brand Colors</h4>
        <p class="text-neutral-cw mb-4">Core brand colors that define the CozyWish identity</p>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #2F160F;"></div>
        <div class="color-info">
          <strong>Brand Primary</strong><br>
          #2F160F
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #4a2a1f;"></div>
        <div class="color-info">
          <strong>Brand Light</strong><br>
          #4a2a1f
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #fae1d7;"></div>
        <div class="color-info">
          <strong>Brand Accent</strong><br>
          #fae1d7
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #fef7f0;"></div>
        <div class="color-info">
          <strong>Accent Light</strong><br>
          #fef7f0
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #f1d4c4;"></div>
        <div class="color-info">
          <strong>Accent Dark</strong><br>
          #f1d4c4
        </div>
      </div>
    </div>

    <!-- Primary Colors -->
    <div class="row mb-5">
      <div class="col-12">
        <h4 class="fw-bold mb-4">Primary Colors</h4>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #fef7f0;"></div>
        <div class="color-info">
          <strong>50</strong><br>
          #fef7f0
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #fdeee0;"></div>
        <div class="color-info">
          <strong>100</strong><br>
          #fdeee0
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #fad9c0;"></div>
        <div class="color-info">
          <strong>200</strong><br>
          #fad9c0
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #f6be95;"></div>
        <div class="color-info">
          <strong>300</strong><br>
          #f6be95
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #ed7544;"></div>
        <div class="color-info">
          <strong>500</strong><br>
          #ed7544
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #de5a2c;"></div>
        <div class="color-info">
          <strong>600</strong><br>
          #de5a2c
        </div>
      </div>
    </div>

    <!-- Secondary Colors -->
    <div class="row mb-5">
      <div class="col-12">
        <h4 class="fw-bold mb-4">Secondary Colors</h4>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #f9f7f4;"></div>
        <div class="color-info">
          <strong>50</strong><br>
          #f9f7f4
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #e3d5c4;"></div>
        <div class="color-info">
          <strong>200</strong><br>
          #e3d5c4
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #ad7f5a;"></div>
        <div class="color-info">
          <strong>500</strong><br>
          #ad7f5a
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #583d30;"></div>
        <div class="color-info">
          <strong>900</strong><br>
          #583d30
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #2f1f18;"></div>
        <div class="color-info">
          <strong>950</strong><br>
          #2f1f18
        </div>
      </div>
    </div>

    <!-- Neutral Colors -->
    <div class="row">
      <div class="col-12">
        <h4 class="fw-bold mb-4">Neutral Colors</h4>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #fafafa;"></div>
        <div class="color-info">
          <strong>50</strong><br>
          #fafafa
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #e5e5e5;"></div>
        <div class="color-info">
          <strong>200</strong><br>
          #e5e5e5
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #737373;"></div>
        <div class="color-info">
          <strong>500</strong><br>
          #737373
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #404040;"></div>
        <div class="color-info">
          <strong>700</strong><br>
          #404040
        </div>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="color-swatch" style="background-color: #171717;"></div>
        <div class="color-info">
          <strong>900</strong><br>
          #171717
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Typography Section -->
<section class="section-showcase" id="typography">
  <div class="container">
    <h2 class="section-title text-center">Typography</h2>

    <div class="row">
      <div class="col-lg-6 mb-5">
        <div class="preview-box">
          <div class="preview-title">Font Families</div>
          <div class="mb-4">
            <h6 class="fw-bold text-primary-cw">Primary Font (Inter)</h6>
            <p style="font-family: var(--cw-font-primary); font-size: 1.125rem;">
              The quick brown fox jumps over the lazy dog.
              <br><strong>Bold text example</strong>
              <br><em>Italic text example</em>
            </p>
          </div>
          <div class="mb-4">
            <h6 class="fw-bold text-primary-cw">Heading Font (Poppins)</h6>
            <p style="font-family: var(--cw-font-heading); font-size: 1.125rem; font-weight: 600;">
              The quick brown fox jumps over the lazy dog.
            </p>
          </div>
          <div>
            <h6 class="fw-bold text-primary-cw">Display Font (Playfair Display)</h6>
            <p style="font-family: var(--cw-font-display); font-size: 1.25rem;">
              The quick brown fox jumps over the lazy dog.
            </p>
          </div>
        </div>
      </div>

      <div class="col-lg-6 mb-5">
        <div class="preview-box">
          <div class="preview-title">Heading Hierarchy</div>
          <h1 class="display-font">Display Heading</h1>
          <h1>Heading 1</h1>
          <h2>Heading 2</h2>
          <h3>Heading 3</h3>
          <h4>Heading 4</h4>
          <h5>Heading 5</h5>
          <h6>Heading 6</h6>
          <p>Body text with <strong>bold</strong>, <em>italic</em>, and <a href="#" class="text-primary-cw">link</a> styles.</p>
          <p class="text-neutral-cw">Muted text for secondary information.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Components Section -->
<section class="section-showcase bg-white" id="components">
  <div class="container">
    <h2 class="section-title text-center">Components</h2>

    <!-- Buttons -->
    <div class="row mb-5">
      <div class="col-12">
        <div class="preview-box">
          <div class="preview-title">Buttons</div>
          <div class="d-flex flex-wrap gap-3 mb-4">
            <button class="btn btn-cw-primary">Primary (Brand)</button>
            <button class="btn btn-cw-secondary">Secondary (Brand)</button>
            <button class="btn btn-cw-orange">Orange Button</button>
            <button class="btn btn-cw-ghost">Ghost Button</button>
          </div>

          <div class="d-flex flex-wrap gap-3 mb-4">
            <button class="btn btn-cw-orange-outline">Orange Outline</button>
            <button class="btn btn-cw-primary" disabled>Disabled Button</button>
          </div>

          <div class="d-flex flex-wrap gap-3 mb-4">
            <button class="btn btn-cw-primary btn-sm">Small</button>
            <button class="btn btn-cw-primary">Medium</button>
            <button class="btn btn-cw-primary btn-lg">Large</button>
          </div>

          <div class="d-flex flex-wrap gap-3">
            <button class="btn btn-outline-secondary rounded-circle" style="width: 48px; height: 48px;">
              <i class="fab fa-google"></i>
            </button>
            <button class="btn btn-outline-secondary rounded-circle" style="width: 48px; height: 48px;">
              <i class="fab fa-facebook-f"></i>
            </button>
            <button class="btn btn-outline-secondary rounded-circle" style="width: 48px; height: 48px;">
              <i class="fab fa-apple"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Forms -->
    <div class="row mb-5">
      <div class="col-lg-6 mb-4">
        <div class="preview-box">
          <div class="preview-title">Form Controls</div>
          <form>
            <div class="mb-3">
              <label class="form-label fw-bold">Name</label>
              <input type="text" class="form-control form-control-cw" placeholder="Enter your name">
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Email</label>
              <input type="email" class="form-control form-control-cw" placeholder="<EMAIL>">
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Service Type</label>
              <select class="form-select form-control-cw">
                <option>Choose a service...</option>
                <option>Massage Therapy</option>
                <option>Facial Treatment</option>
                <option>Nail Services</option>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Message</label>
              <textarea class="form-control form-control-cw" rows="3" placeholder="Tell us about your needs..."></textarea>
            </div>
            <div class="form-check mb-3">
              <input class="form-check-input" type="checkbox" id="terms">
              <label class="form-check-label" for="terms">
                I agree to the terms and conditions
              </label>
            </div>
            <button type="submit" class="btn btn-cw-primary w-100">Submit Request</button>
          </form>
        </div>
      </div>

      <div class="col-lg-6 mb-4">
        <div class="preview-box">
          <div class="preview-title">Badges & Alerts</div>

          <div class="mb-4">
            <h6 class="fw-bold mb-3">Badges</h6>
            <div class="d-flex flex-wrap gap-2">
              <span class="badge-cw-primary">Featured</span>
              <span class="badge-cw-secondary">Popular</span>
              <span class="badge bg-success">Available</span>
              <span class="badge bg-warning text-dark">Limited</span>
              <span class="badge bg-danger">Sold Out</span>
            </div>
          </div>

          <div>
            <h6 class="fw-bold mb-3">Alerts</h6>
            <div class="alert-cw-success alert mb-2">
              <i class="fas fa-check-circle me-2"></i>
              Booking confirmed successfully!
            </div>
            <div class="alert-cw-warning alert mb-2">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Please verify your email address.
            </div>
            <div class="alert-cw-error alert">
              <i class="fas fa-times-circle me-2"></i>
              Unable to process payment.
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cards -->
    <div class="row mb-5">
      <div class="col-12">
        <div class="preview-box">
          <div class="preview-title">Cards</div>
          <div class="row g-4">
            <div class="col-lg-4">
              <div class="card-cw h-100">
                <img src="https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" class="card-img-top" alt="Spa Service" style="height: 200px; object-fit: cover;">
                <div class="card-body p-4">
                  <h5 class="card-title fw-bold">Relaxing Massage</h5>
                  <p class="card-text text-neutral-cw">Experience ultimate relaxation with our signature massage therapy.</p>
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="badge-cw-primary">$89</span>
                    <button class="btn btn-cw-primary btn-sm">Book Now</button>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-4">
              <div class="card-cw-brand h-100">
                <img src="https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" class="card-img-top" alt="Facial Treatment" style="height: 200px; object-fit: cover;">
                <div class="card-body p-4">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <h5 class="card-title fw-bold text-brand-cw">Premium Facial</h5>
                    <span class="badge-cw-primary">Brand</span>
                  </div>
                  <p class="card-text text-neutral-cw">Rejuvenate your skin with our premium facial treatment package.</p>
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="badge-cw-primary">$129</span>
                    <button class="btn btn-cw-primary btn-sm">Book Now</button>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-4">
              <div class="card-cw-accent h-100">
                <div class="card-body p-4">
                  <h5 class="card-title fw-bold text-brand-cw">Special Offer</h5>
                  <p class="card-text text-brand-light-cw">Get 20% off your first spa treatment when you book this month!</p>
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="badge-cw-secondary">Limited Time</span>
                    <button class="btn btn-cw-secondary btn-sm">Learn More</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Additional Card Row -->
          <div class="row g-4 mt-3">
            <div class="col-lg-4">
              <div class="card-cw h-100">
                <img src="https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" class="card-img-top" alt="Nail Service" style="height: 200px; object-fit: cover;">
                <div class="card-body p-4">
                  <h5 class="card-title fw-bold">Nail Care</h5>
                  <p class="card-text text-neutral-cw">Professional nail care services for hands and feet.</p>
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="badge-cw-primary">$45</span>
                    <button class="btn btn-cw-orange-outline btn-sm">Book Now</button>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-4">
              <div class="card-cw-featured h-100">
                <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" class="card-img-top" alt="Spa Package" style="height: 200px; object-fit: cover;">
                <div class="card-body p-4">
                  <h5 class="card-title fw-bold">Spa Package</h5>
                  <p class="card-text text-neutral-cw">Complete relaxation package with massage and facial.</p>
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="badge-cw-primary">$199</span>
                    <button class="btn btn-cw-orange btn-sm">Book Now</button>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-4">
              <div class="card-cw h-100">
                <img src="https://images.unsplash.com/photo-1559599101-f09722fb4948?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" class="card-img-top" alt="Wellness" style="height: 200px; object-fit: cover;">
                <div class="card-body p-4">
                  <h5 class="card-title fw-bold">Wellness Session</h5>
                  <p class="card-text text-neutral-cw">Holistic wellness experience for mind and body.</p>
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="badge-cw-primary">$75</span>
                    <button class="btn btn-cw-orange-outline btn-sm">Book Now</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Layouts Section -->
<section class="section-showcase" id="layouts">
  <div class="container">
    <h2 class="section-title text-center">Layout Examples</h2>

    <!-- Navigation Patterns -->
    <div class="row mb-5">
      <div class="col-12">
        <div class="preview-box">
          <div class="preview-title">Navigation & Breadcrumbs</div>

          <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
              <li class="breadcrumb-item"><a href="#" class="text-primary-cw text-decoration-none">Home</a></li>
              <li class="breadcrumb-item"><a href="#" class="text-primary-cw text-decoration-none">Services</a></li>
              <li class="breadcrumb-item active" aria-current="page">Massage Therapy</li>
            </ol>
          </nav>

          <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
              <li class="page-item disabled">
                <span class="page-link">Previous</span>
              </li>
              <li class="page-item active">
                <span class="page-link">1</span>
              </li>
              <li class="page-item">
                <a class="page-link text-primary-cw" href="#">2</a>
              </li>
              <li class="page-item">
                <a class="page-link text-primary-cw" href="#">3</a>
              </li>
              <li class="page-item">
                <a class="page-link text-primary-cw" href="#">Next</a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>

    <!-- Tables -->
    <div class="row mb-5">
      <div class="col-12">
        <div class="preview-box">
          <div class="preview-title">Data Tables</div>

          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th scope="col">#</th>
                  <th scope="col">Service</th>
                  <th scope="col">Provider</th>
                  <th scope="col">Price</th>
                  <th scope="col">Status</th>
                  <th scope="col">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <th scope="row">1</th>
                  <td>Deep Tissue Massage</td>
                  <td>Serenity Spa</td>
                  <td>$89</td>
                  <td><span class="badge bg-success">Available</span></td>
                  <td>
                    <button class="btn btn-cw-primary btn-sm">Book</button>
                  </td>
                </tr>
                <tr>
                  <th scope="row">2</th>
                  <td>Facial Treatment</td>
                  <td>Glow Beauty</td>
                  <td>$129</td>
                  <td><span class="badge bg-warning text-dark">Limited</span></td>
                  <td>
                    <button class="btn btn-cw-orange-outline btn-sm">Book</button>
                  </td>
                </tr>
                <tr>
                  <th scope="row">3</th>
                  <td>Manicure & Pedicure</td>
                  <td>Nail Studio</td>
                  <td>$45</td>
                  <td><span class="badge bg-danger">Booked</span></td>
                  <td>
                    <button class="btn btn-outline-secondary btn-sm" disabled>Unavailable</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Example -->
    <div class="row mb-5">
      <div class="col-12">
        <div class="preview-box">
          <div class="preview-title">Modal Dialog</div>
          <button class="btn btn-cw-primary" data-bs-toggle="modal" data-bs-target="#exampleModal">
            Launch Demo Modal
          </button>
        </div>
      </div>
    </div>

    <!-- Search & Filters -->
    <div class="row">
      <div class="col-12">
        <div class="preview-box">
          <div class="preview-title">Search & Filter Layout</div>

          <div class="row g-3">
            <div class="col-md-4">
              <div class="input-group">
                <span class="input-group-text bg-light">
                  <i class="fas fa-search text-primary-cw"></i>
                </span>
                <input type="text" class="form-control form-control-cw" placeholder="Search services...">
              </div>
            </div>
            <div class="col-md-3">
              <select class="form-select form-control-cw">
                <option>All Categories</option>
                <option>Massage</option>
                <option>Facial</option>
                <option>Nails</option>
              </select>
            </div>
            <div class="col-md-3">
              <select class="form-select form-control-cw">
                <option>Any Price</option>
                <option>Under $50</option>
                <option>$50 - $100</option>
                <option>Over $100</option>
              </select>
            </div>
            <div class="col-md-2">
              <button class="btn btn-cw-primary w-100">Filter</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Modal -->
<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content" style="border: none; border-radius: 1rem; box-shadow: var(--cw-shadow-xl);">
      <div class="modal-header border-0">
        <h5 class="modal-title fw-bold text-secondary-cw" id="exampleModalLabel">Book Your Service</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="text-neutral-cw">This modal demonstrates the CozyWish design system styling with proper shadows, colors, and typography.</p>
        <div class="d-flex align-items-center p-3 bg-light-cw rounded-cw">
          <i class="fas fa-spa text-primary-cw fa-2x me-3"></i>
          <div>
            <h6 class="fw-bold mb-1">Deep Tissue Massage</h6>
            <p class="text-neutral-cw mb-0">60 minutes • $89</p>
          </div>
        </div>
      </div>
      <div class="modal-footer border-0">
        <button type="button" class="btn btn-cw-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-cw-primary">Confirm Booking</button>
      </div>
    </div>
  </div>
</div>

<!-- Footer -->
<footer class="section-showcase bg-white border-top">
  <div class="container">
    <div class="row">
      <div class="col-lg-4 mb-4">
        <h5 class="fw-bold text-secondary-cw mb-3">CozyWish</h5>
        <p class="text-neutral-cw">Find & Book Local Spa and Massage Services</p>
        <p class="text-neutral-cw">Professional design system for wellness marketplace applications.</p>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
        <h6 class="fw-bold text-secondary-cw mb-3">Services</h6>
        <ul class="list-unstyled">
          <li><a href="#" class="text-neutral-cw text-decoration-none">Massage</a></li>
          <li><a href="#" class="text-neutral-cw text-decoration-none">Facial</a></li>
          <li><a href="#" class="text-neutral-cw text-decoration-none">Nails</a></li>
          <li><a href="#" class="text-neutral-cw text-decoration-none">Spa</a></li>
        </ul>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
        <h6 class="fw-bold text-secondary-cw mb-3">Company</h6>
        <ul class="list-unstyled">
          <li><a href="#" class="text-neutral-cw text-decoration-none">About</a></li>
          <li><a href="#" class="text-neutral-cw text-decoration-none">Careers</a></li>
          <li><a href="#" class="text-neutral-cw text-decoration-none">Contact</a></li>
          <li><a href="#" class="text-neutral-cw text-decoration-none">Blog</a></li>
        </ul>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
        <h6 class="fw-bold text-secondary-cw mb-3">Support</h6>
        <ul class="list-unstyled">
          <li><a href="#" class="text-neutral-cw text-decoration-none">Help Center</a></li>
          <li><a href="#" class="text-neutral-cw text-decoration-none">Privacy</a></li>
          <li><a href="#" class="text-neutral-cw text-decoration-none">Terms</a></li>
          <li><a href="#" class="text-neutral-cw text-decoration-none">FAQ</a></li>
        </ul>
      </div>
      <div class="col-lg-2 col-md-3 col-sm-6 mb-4">
        <h6 class="fw-bold text-secondary-cw mb-3">Connect</h6>
        <div class="d-flex gap-2">
          <a href="#" class="btn btn-outline-secondary btn-sm rounded-circle" style="width: 40px; height: 40px;">
            <i class="fab fa-facebook-f"></i>
          </a>
          <a href="#" class="btn btn-outline-secondary btn-sm rounded-circle" style="width: 40px; height: 40px;">
            <i class="fab fa-twitter"></i>
          </a>
          <a href="#" class="btn btn-outline-secondary btn-sm rounded-circle" style="width: 40px; height: 40px;">
            <i class="fab fa-instagram"></i>
          </a>
        </div>
      </div>
    </div>
    <hr class="my-4">
    <div class="row align-items-center">
      <div class="col-md-6">
        <p class="text-neutral-cw mb-0">&copy; 2025 CozyWish. All rights reserved.</p>
      </div>
      <div class="col-md-6 text-md-end">
        <p class="text-neutral-cw mb-0">Design System v2.0.0</p>
      </div>
    </div>
  </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Smooth scrolling for navigation -->
<script>
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
</script>

</body>
</html>
